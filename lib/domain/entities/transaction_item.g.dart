// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transaction_item.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$TransactionItemEntityImpl _$$TransactionItemEntityImplFromJson(
  Map<String, dynamic> json,
) => _$TransactionItemEntityImpl(
  id: (json['id'] as num).toInt(),
  transactionId: (json['transactionId'] as num).toInt(),
  itemId: (json['itemId'] as num).toInt(),
  quantity: (json['quantity'] as num).toInt(),
  priceAtPurchase: (json['priceAtPurchase'] as num).toDouble(),
  createdAt: DateTime.parse(json['createdAt'] as String),
);

Map<String, dynamic> _$$TransactionItemEntityImplToJson(
  _$TransactionItemEntityImpl instance,
) => <String, dynamic>{
  'id': instance.id,
  'transactionId': instance.transactionId,
  'itemId': instance.itemId,
  'quantity': instance.quantity,
  'priceAtPurchase': instance.priceAtPurchase,
  'createdAt': instance.createdAt.toIso8601String(),
};

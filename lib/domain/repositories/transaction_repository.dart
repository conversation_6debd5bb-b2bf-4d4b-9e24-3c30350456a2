import '../entities/transaction.dart';
import '../entities/transaction_with_items.dart';
import '../entities/transaction_with_payments.dart';
import '../entities/complete_transaction_details.dart';
import '../entities/transaction_item.dart';
import '../entities/payment.dart';
import '../entities/paid_item.dart';

abstract class TransactionRepository {
  Future<List<TransactionEntity>> getAllTransactions();
  Future<List<TransactionEntity>> getUnpaidTransactions();
  Future<List<TransactionEntity>> getPaidTransactions();
  Future<TransactionEntity> getTransactionById(int id);
  Future<TransactionWithItemsEntity> getTransactionWithItems(int transactionId);
  Future<TransactionWithPaymentsEntity> getTransactionWithPayments(int transactionId);
  Future<CompleteTransactionDetailsEntity> getCompleteTransactionDetails(int transactionId);
  Future<int> createTransactionWithItems(
    TransactionEntity transaction,
    List<TransactionItemEntity> items,
  );
  Future<int> addPaymentAndUpdateTransaction(
    PaymentEntity payment,
    TransactionEntity updatedTransaction,
    List<PaidItemEntity> paidItems,
  );
  Future<List<PaidItemEntity>> getPaymentPaidItems(int paymentId);
  Future<List<PaidItemEntity>> getPaidItemsForTransactionItem(int transactionItemId);
}

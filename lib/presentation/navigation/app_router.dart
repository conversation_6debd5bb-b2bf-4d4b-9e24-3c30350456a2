import 'package:go_router/go_router.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/constants/app_constants.dart';
import '../screens/items/items_screen.dart';
import '../screens/new_transaction/new_transaction_screen.dart';
import '../screens/unpaid_transactions/enhanced_unpaid_transactions_screen.dart';
import '../screens/transaction_history/transaction_history_screen.dart';
import '../screens/main_screen.dart';

final routerProvider = Provider<GoRouter>((ref) {
  return GoRouter(
    initialLocation: AppConstants.itemsRoute,
    routes: [
      ShellRoute(
        builder: (context, state, child) {
          return MainScreen(child: child);
        },
        routes: [
          GoRoute(
            path: AppConstants.itemsRoute,
            name: 'items',
            pageBuilder: (context, state) => NoTransitionPage(
              key: state.pageKey,
              child: const ItemsScreen(),
            ),
          ),
          GoRoute(
            path: AppConstants.newTransactionRoute,
            name: 'newTransaction',
            pageBuilder: (context, state) => NoTransitionPage(
              key: state.pageKey,
              child: const NewTransactionScreen(),
            ),
          ),
          GoRoute(
            path: AppConstants.unpaidTransactionsRoute,
            name: 'unpaidTransactions',
            pageBuilder: (context, state) => NoTransitionPage(
              key: state.pageKey,
              child: const EnhancedUnpaidTransactionsScreen(),
            ),
          ),
          GoRoute(
            path: AppConstants.transactionHistoryRoute,
            name: 'transactionHistory',
            pageBuilder: (context, state) => NoTransitionPage(
              key: state.pageKey,
              child: const TransactionHistoryScreen(),
            ),
          ),
        ],
      ),
    ],
  );
});

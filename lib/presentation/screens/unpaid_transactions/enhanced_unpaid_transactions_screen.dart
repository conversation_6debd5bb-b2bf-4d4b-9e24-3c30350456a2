import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/utils/formatters.dart';
import '../../providers/unpaid_transaction_items_provider.dart';
import '../../widgets/empty_state.dart';
import '../../widgets/error_display.dart';
import '../../widgets/loading_indicator.dart';
import 'widgets/unpaid_item_card.dart';

class EnhancedUnpaidTransactionsScreen extends ConsumerWidget {
  const EnhancedUnpaidTransactionsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final unpaidItemsState = ref.watch(unpaidTransactionItemsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text(AppConstants.titleUnpaidTransactions),
      ),
      body: RefreshIndicator(
        onRefresh: () => ref.read(unpaidTransactionItemsProvider.notifier).refreshUnpaidItems(),
        child: unpaidItemsState.when(
          data: (unpaidItems) {
            if (unpaidItems.isEmpty) {
              return const EmptyState(
                message: AppConstants.emptyUnpaidTransactions,
                icon: Icons.payment_outlined,
              );
            }

            // Calculate grand total
            final grandTotal = ref.read(unpaidTransactionItemsProvider.notifier).getGrandTotal();

            return Column(
              children: [
                // Grand Total and Pay All Button
                Container(
                  width: double.infinity,
                  margin: const EdgeInsets.all(AppConstants.defaultPadding),
                  child: Card(
                    child: Padding(
                      padding: const EdgeInsets.all(AppConstants.defaultPadding),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            AppConstants.labelGrandTotal,
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                          const SizedBox(height: AppConstants.smallPadding),
                          Text(
                            Formatters.formatCurrency(grandTotal),
                            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                          ),
                          const SizedBox(height: AppConstants.defaultPadding),
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton.icon(
                              onPressed: grandTotal > 0
                                  ? () => _showGlobalPaymentBottomSheet(context, ref, grandTotal)
                                  : null,
                              icon: const Icon(Icons.payment),
                              label: const Text(AppConstants.buttonPayAll),
                              style: ElevatedButton.styleFrom(
                                padding: const EdgeInsets.symmetric(vertical: 12),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),

                // Unpaid Items List
                Expanded(
                  child: ListView.builder(
                    padding: const EdgeInsets.all(AppConstants.defaultPadding),
                    itemCount: unpaidItems.length,
                    itemBuilder: (context, index) {
                      final unpaidItem = unpaidItems[index];
                      return UnpaidItemCard(
                        unpaidItem: unpaidItem,
                      );
                    },
                  ),
                ),
              ],
            );
          },
          loading: () => const LoadingIndicator(),
          error: (error, stackTrace) => ErrorDisplay(
            message: 'Error loading unpaid items: $error',
            onRetry: () => ref.read(unpaidTransactionItemsProvider.notifier).refreshUnpaidItems(),
          ),
        ),
      ),
    );
  }

  void _showGlobalPaymentBottomSheet(BuildContext context, WidgetRef ref, double grandTotal) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Padding(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: EnhancedGlobalPaymentBottomSheet(
          grandTotal: grandTotal,
        ),
      ),
    );
  }
}

// Enhanced global payment bottom sheet that uses the new provider
class EnhancedGlobalPaymentBottomSheet extends ConsumerStatefulWidget {
  final double grandTotal;

  const EnhancedGlobalPaymentBottomSheet({
    super.key,
    required this.grandTotal,
  });

  @override
  ConsumerState<EnhancedGlobalPaymentBottomSheet> createState() => _EnhancedGlobalPaymentBottomSheetState();
}

class _EnhancedGlobalPaymentBottomSheetState extends ConsumerState<EnhancedGlobalPaymentBottomSheet> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final DateTime _paymentDate = DateTime.now();
  bool _isLoading = false;

  @override
  void dispose() {
    _amountController.dispose();
    super.dispose();
  }

  Future<void> _makeGlobalPayment() async {
    if (_formKey.currentState?.validate() != true) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    final amount = Formatters.parseCurrency(_amountController.text);

    final success = await ref.read(unpaidTransactionItemsProvider.notifier).addGlobalPayment(
      amount,
      _paymentDate,
    );

    setState(() {
      _isLoading = false;
    });

    if (success && mounted) {
      Navigator.of(context).pop();
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text(AppConstants.successPaymentAdded)),
      );
    } else if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Failed to add payment')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(AppConstants.bottomSheetBorderRadius),
        ),
      ),
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppConstants.titleGlobalPayment,
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            Text(
              '${AppConstants.labelGrandTotal}: ${Formatters.formatCurrency(widget.grandTotal)}',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.smallPadding),
            Text(
              '${AppConstants.labelDate}: ${Formatters.formatDate(_paymentDate)}',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppConstants.defaultPadding),
            TextFormField(
              controller: _amountController,
              decoration: const InputDecoration(
                labelText: AppConstants.labelPaymentAmount,
                prefixText: AppConstants.currencySymbol,
              ),
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return AppConstants.errorRequiredField;
                }
                final amount = Formatters.parseCurrency(value);
                if (amount <= 0) {
                  return AppConstants.errorMinPrice;
                }
                if (amount > widget.grandTotal) {
                  return AppConstants.errorExceedingPayment;
                }
                return null;
              },
            ),
            const SizedBox(height: AppConstants.largePadding),
            ElevatedButton(
              onPressed: _isLoading ? null : _makeGlobalPayment,
              child: _isLoading
                  ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                      ),
                    )
                  : const Text(AppConstants.buttonPay),
            ),
          ],
        ),
      ),
    );
  }
}

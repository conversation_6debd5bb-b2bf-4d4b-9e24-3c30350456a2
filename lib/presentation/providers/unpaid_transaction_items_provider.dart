import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/constants/app_constants.dart';
import '../../domain/entities/transaction.dart';
import '../../domain/entities/transaction_item_with_details.dart';
import '../../domain/entities/transaction_item.dart';
import '../../domain/entities/item.dart';
import '../../domain/entities/payment.dart';
import '../../domain/entities/paid_item.dart';
import '../../domain/usecases/transaction_usecases.dart';
import 'providers.dart';
import 'transaction_history_provider.dart';

// Enhanced unpaid transaction items provider that works with item-level tracking
class UnpaidTransactionItemsNotifier extends StateNotifier<AsyncValue<List<TransactionItemWithDetailsEntity>>> {
  final GetUnpaidTransactionItemsUseCase _getUnpaidTransactionItemsUseCase;
  final AddPaymentAndUpdateTransactionUseCase _addPaymentAndUpdateTransactionUseCase;
  final UpdateTransactionItemRemainingAmountUseCase _updateTransactionItemRemainingAmountUseCase;
  final RecalculateTransactionRemainingAmountUseCase _recalculateTransactionRemainingAmountUseCase;
  final Ref _ref;

  UnpaidTransactionItemsNotifier({
    required GetUnpaidTransactionItemsUseCase getUnpaidTransactionItemsUseCase,
    required AddPaymentAndUpdateTransactionUseCase addPaymentAndUpdateTransactionUseCase,
    required UpdateTransactionItemRemainingAmountUseCase updateTransactionItemRemainingAmountUseCase,
    required RecalculateTransactionRemainingAmountUseCase recalculateTransactionRemainingAmountUseCase,
    required Ref ref,
  })  : _getUnpaidTransactionItemsUseCase = getUnpaidTransactionItemsUseCase,
        _addPaymentAndUpdateTransactionUseCase = addPaymentAndUpdateTransactionUseCase,
        _updateTransactionItemRemainingAmountUseCase = updateTransactionItemRemainingAmountUseCase,
        _recalculateTransactionRemainingAmountUseCase = recalculateTransactionRemainingAmountUseCase,
        _ref = ref,
        super(const AsyncValue.loading()) {
    refreshUnpaidItems();
  }

  // Calculate the grand total of all unpaid items
  double getGrandTotal() {
    if (state is! AsyncData) {
      return 0.0;
    }

    final unpaidItems = (state as AsyncData<List<TransactionItemWithDetailsEntity>>).value;
    return unpaidItems.fold(0.0, (sum, item) => sum + item.transactionItem.remainingAmount);
  }

  Future<void> refreshUnpaidItems() async {
    state = const AsyncValue.loading();
    try {
      final unpaidItems = await _getUnpaidTransactionItemsUseCase();
      state = AsyncValue.data(unpaidItems);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  // Enhanced global payment that allocates to individual items
  Future<bool> addGlobalPayment(double amount, DateTime paymentDate) async {
    try {
      if (state is! AsyncData || amount <= 0) {
        return false;
      }

      // Get all unpaid items
      final unpaidItems = List<TransactionItemWithDetailsEntity>.from(
        (state as AsyncData<List<TransactionItemWithDetailsEntity>>).value
      );

      if (unpaidItems.isEmpty) {
        return false;
      }

      // Sort items by remaining amount (ascending) to prioritize full payments
      unpaidItems.sort((a, b) => a.transactionItem.remainingAmount.compareTo(b.transactionItem.remainingAmount));

      // Select items for payment using enhanced algorithm
      final selectedItems = _selectItemsForPayment(unpaidItems, amount);

      if (selectedItems.isEmpty) {
        return false;
      }

      // Group selected items by transaction for processing
      final itemsByTransaction = <int, List<_SelectedItemPayment>>{};
      for (final item in selectedItems) {
        final transactionId = item.transactionItem.transactionId;
        itemsByTransaction.putIfAbsent(transactionId, () => []).add(item);
      }

      bool atLeastOnePaymentMade = false;

      // Process each transaction that has selected items
      for (final entry in itemsByTransaction.entries) {
        final transactionId = entry.key;
        final selectedItemsForTransaction = entry.value;

        // Calculate total amount being paid for this transaction
        final totalPaidForTransaction = selectedItemsForTransaction
            .fold(0.0, (sum, item) => sum + item.paidAmount);

        // Create payment entity
        final payment = PaymentEntity(
          id: 0,
          transactionId: transactionId,
          amount: totalPaidForTransaction,
          date: paymentDate,
          createdAt: DateTime.now(),
        );

        // Create paid items entities for selected items
        final paidItems = <PaidItemEntity>[];
        for (final selectedItem in selectedItemsForTransaction) {
          paidItems.add(
            PaidItemEntity(
              id: 0,
              paymentId: 0, // Will be set by the database
              transactionItemId: selectedItem.transactionItem.id,
              quantity: selectedItem.paidQuantity,
              amount: selectedItem.paidAmount,
              createdAt: DateTime.now(),
            ),
          );
        }

        try {
          // Create a dummy transaction entity for the payment (will be recalculated anyway)
          final dummyTransaction = TransactionEntity(
            id: transactionId,
            date: DateTime.now(),
            totalAmount: 0, // Will be recalculated
            remainingAmount: 0, // Will be recalculated
            status: AppConstants.statusUnpaid, // Will be recalculated
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );

          // Add payment with paid items
          await _addPaymentAndUpdateTransactionUseCase(payment, dummyTransaction, paidItems);

          // Update remaining amounts for each item
          for (final selectedItem in selectedItemsForTransaction) {
            final newRemainingAmount = selectedItem.transactionItem.remainingAmount - selectedItem.paidAmount;
            await _updateTransactionItemRemainingAmountUseCase(
              selectedItem.transactionItem.id,
              newRemainingAmount,
            );
          }

          // Recalculate transaction remaining amount and status
          await _recalculateTransactionRemainingAmountUseCase(transactionId);

          atLeastOnePaymentMade = true;
        } catch (e) {
          debugPrint('Error updating transaction $transactionId: $e');
        }
      }

      // Refresh unpaid items list
      await refreshUnpaidItems();

      // Refresh transaction history if any payments were made
      if (atLeastOnePaymentMade) {
        _ref.read(baseTransactionHistoryProvider.notifier).refreshTransactions();
      }

      return atLeastOnePaymentMade;
    } catch (e) {
      debugPrint('Error in addGlobalPayment: $e');
      return false;
    }
  }

  // Enhanced item selection algorithm that prioritizes full payments
  List<_SelectedItemPayment> _selectItemsForPayment(
    List<TransactionItemWithDetailsEntity> unpaidItems,
    double paymentAmount,
  ) {
    final selectedItems = <_SelectedItemPayment>[];
    double remainingAmount = paymentAmount;

    // First pass: Try to fully pay as many items as possible
    for (final item in unpaidItems) {
      final itemRemainingAmount = item.transactionItem.remainingAmount;

      if (itemRemainingAmount <= remainingAmount) {
        // Can fully pay this item
        selectedItems.add(_SelectedItemPayment(
          transactionItem: item.transactionItem,
          item: item.item,
          paidAmount: itemRemainingAmount,
          paidQuantity: item.transactionItem.quantity,
          isFullPayment: true,
        ));
        remainingAmount -= itemRemainingAmount;

        if (remainingAmount == 0) {
          break;
        }
      }
    }

    // Second pass: If there's remaining payment amount, apply partial payment to next item
    if (remainingAmount > 0) {
      for (final item in unpaidItems) {
        final itemRemainingAmount = item.transactionItem.remainingAmount;

        // Skip items already fully paid
        if (selectedItems.any((selected) =>
            selected.transactionItem.id == item.transactionItem.id && selected.isFullPayment)) {
          continue;
        }

        if (itemRemainingAmount > remainingAmount) {
          // Apply partial payment to this item
          final unitPrice = item.transactionItem.priceAtPurchase;
          final partialQuantity = (remainingAmount / unitPrice).floor();

          if (partialQuantity > 0) {
            selectedItems.add(_SelectedItemPayment(
              transactionItem: item.transactionItem,
              item: item.item,
              paidAmount: remainingAmount,
              paidQuantity: partialQuantity,
              isFullPayment: false,
            ));
          }
          break;
        }
      }
    }

    return selectedItems;
  }
}

// Helper class to track selected items for payment
class _SelectedItemPayment {
  final TransactionItemEntity transactionItem;
  final ItemEntity item;
  final double paidAmount;
  final int paidQuantity;
  final bool isFullPayment;

  _SelectedItemPayment({
    required this.transactionItem,
    required this.item,
    required this.paidAmount,
    required this.paidQuantity,
    required this.isFullPayment,
  });
}

// Provider for unpaid transaction items
final unpaidTransactionItemsProvider = StateNotifierProvider<UnpaidTransactionItemsNotifier, AsyncValue<List<TransactionItemWithDetailsEntity>>>((ref) {
  return UnpaidTransactionItemsNotifier(
    getUnpaidTransactionItemsUseCase: ref.watch(getUnpaidTransactionItemsUseCaseProvider),
    addPaymentAndUpdateTransactionUseCase: ref.watch(addPaymentAndUpdateTransactionUseCaseProvider),
    updateTransactionItemRemainingAmountUseCase: ref.watch(updateTransactionItemRemainingAmountUseCaseProvider),
    recalculateTransactionRemainingAmountUseCase: ref.watch(recalculateTransactionRemainingAmountUseCaseProvider),
    ref: ref,
  );
});

import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/constants/app_constants.dart';
import '../../domain/entities/item.dart';
import '../../domain/entities/transaction.dart';
import '../../domain/entities/transaction_item.dart';
import '../../domain/usecases/transaction_usecases.dart';
import 'providers.dart';
import 'unpaid_transactions_provider.dart';

// Transaction item with quantity
class TransactionItemWithQuantity {
  final ItemEntity item;
  final int quantity;

  TransactionItemWithQuantity({
    required this.item,
    required this.quantity,
  });

  double get totalPrice => item.price * quantity;
}

// New transaction state
class NewTransactionState {
  final DateTime date;
  final List<TransactionItemWithQuantity> items;
  final bool isLoading;

  NewTransactionState({
    required this.date,
    required this.items,
    this.isLoading = false,
  });

  double get totalAmount {
    return items.fold(0, (sum, item) => sum + item.totalPrice);
  }

  NewTransactionState copyWith({
    DateTime? date,
    List<TransactionItemWithQuantity>? items,
    bool? isLoading,
  }) {
    return NewTransactionState(
      date: date ?? this.date,
      items: items ?? this.items,
      isLoading: isLoading ?? this.isLoading,
    );
  }
}

// New transaction notifier
class NewTransactionNotifier extends StateNotifier<NewTransactionState> {
  final CreateTransactionWithItemsUseCase _createTransactionWithItemsUseCase;
  final Ref _ref;

  NewTransactionNotifier({
    required CreateTransactionWithItemsUseCase createTransactionWithItemsUseCase,
    required Ref ref,
  })  : _createTransactionWithItemsUseCase = createTransactionWithItemsUseCase,
        _ref = ref,
        super(
          NewTransactionState(
            date: DateTime.now(),
            items: [],
          ),
        );

  void setDate(DateTime date) {
    state = state.copyWith(date: date);
  }

  void addItem(ItemEntity item, int quantity) {
    // Check if item already exists in the transaction
    final existingIndex = state.items.indexWhere((i) => i.item.id == item.id);

    if (existingIndex >= 0) {
      // Update quantity if item already exists
      updateItemQuantity(existingIndex, state.items[existingIndex].quantity + quantity);
    } else {
      // Add new item
      final newItems = List<TransactionItemWithQuantity>.from(state.items)
        ..add(
          TransactionItemWithQuantity(
            item: item,
            quantity: quantity,
          ),
        );

      state = state.copyWith(items: newItems);
    }
  }

  void updateItemQuantity(int index, int quantity) {
    if (index < 0 || index >= state.items.length || quantity < 1) {
      return;
    }

    final newItems = List<TransactionItemWithQuantity>.from(state.items);
    newItems[index] = TransactionItemWithQuantity(
      item: state.items[index].item,
      quantity: quantity,
    );

    state = state.copyWith(items: newItems);
  }

  void removeItem(int index) {
    if (index < 0 || index >= state.items.length) {
      return;
    }

    final newItems = List<TransactionItemWithQuantity>.from(state.items)
      ..removeAt(index);

    state = state.copyWith(items: newItems);
  }

  void resetTransaction() {
    state = NewTransactionState(
      date: DateTime.now(),
      items: [],
    );
  }

  Future<bool> createTransaction() async {
    if (state.items.isEmpty) {
      return false;
    }

    state = state.copyWith(isLoading: true);

    try {
      final totalAmount = state.totalAmount;

      final transaction = TransactionEntity(
        id: 0, // Will be ignored by the database
        date: state.date,
        totalAmount: totalAmount,
        remainingAmount: totalAmount,
        status: AppConstants.statusUnpaid,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final transactionItems = state.items.map((item) {
        return TransactionItemEntity(
          id: 0, // Will be ignored by the database
          transactionId: 0, // Will be set by the database
          itemId: item.item.id,
          quantity: item.quantity,
          priceAtPurchase: item.item.price,
          createdAt: DateTime.now(),
        );
      }).toList();

      await _createTransactionWithItemsUseCase(transaction, transactionItems);

      // Reset transaction after successful creation
      resetTransaction();

      // Refresh unpaid transactions to show the new transaction
      _ref.read(unpaidTransactionsProvider.notifier).refreshTransactions();

      return true;
    } catch (e) {
      return false;
    } finally {
      state = state.copyWith(isLoading: false);
    }
  }
}

// New transaction provider
final newTransactionProvider = StateNotifierProvider<NewTransactionNotifier, NewTransactionState>((ref) {
  return NewTransactionNotifier(
    createTransactionWithItemsUseCase: ref.watch(createTransactionWithItemsUseCaseProvider),
    ref: ref,
  );
});
